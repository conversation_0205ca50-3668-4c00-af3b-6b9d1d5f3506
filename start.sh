#!/bin/bash

# 教育报名云平台启动脚本

echo "=== 教育报名云平台启动脚本 ==="
echo

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

# 检查Docker Compose是否安装
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose未安装，请先安装Docker Compose"
    exit 1
fi

# 检查Maven是否安装
if ! command -v mvn &> /dev/null; then
    echo "❌ Maven未安装，请先安装Maven"
    exit 1
fi

# 检查Java是否安装
if ! command -v java &> /dev/null; then
    echo "❌ Java未安装，请先安装JDK 8+"
    exit 1
fi

echo "✅ 环境检查通过"
echo

# 启动中间件
# echo "🚀 启动中间件服务..."
# docker-compose up -d

# echo "⏳ 等待中间件启动完成..."
# sleep 30

# 检查中间件状态
echo "📊 检查中间件状态:"
echo "MySQL: $(docker ps --filter name=edu-mysql --format 'table {{.Status}}')"
echo "Nacos: $(docker ps --filter name=edu-nacos --format 'table {{.Status}}')"
echo "Sentinel: $(docker ps --filter name=edu-sentinel --format 'table {{.Status}}')"
echo "Seata: $(docker ps --filter name=edu-seata --format 'table {{.Status}}')"
echo "Redis: $(docker ps --filter name=edu-redis --format 'table {{.Status}}')"
echo

# 编译项目
echo "🔨 编译项目..."
mvn clean install -DskipTests

if [ $? -ne 0 ]; then
    echo "❌ 项目编译失败"
    exit 1
fi

echo "✅ 项目编译成功"
echo

# 启动微服务
echo "🚀 启动微服务..."

# 启动网关服务
echo "启动网关服务 (端口: 8080)..."
cd edu-gateway
nohup mvn spring-boot:run > ../logs/gateway.log 2>&1 &
GATEWAY_PID=$!
cd ..

sleep 10

# 启动用户服务
echo "启动用户服务 (端口: 8081)..."
cd edu-user-service
nohup mvn spring-boot:run > ../logs/user-service.log 2>&1 &
USER_SERVICE_PID=$!
cd ..

sleep 10

# 启动课程服务
echo "启动课程服务 (端口: 8082)..."
cd edu-course-service
nohup mvn spring-boot:run > ../logs/course-service.log 2>&1 &
COURSE_SERVICE_PID=$!
cd ..

sleep 10

# 启动认证服务
echo "启动认证服务 (端口: 8083)..."
cd edu-auth-service
nohup mvn spring-boot:run > ../logs/auth-service.log 2>&1 &
AUTH_SERVICE_PID=$!
cd ..

sleep 10

echo
echo "🎉 所有服务启动完成!"
echo
echo "📋 服务信息:"
echo "┌─────────────────────────────────────────────────────────────┐"
echo "│ 服务名称          │ 端口  │ 访问地址                        │"
echo "├─────────────────────────────────────────────────────────────┤"
echo "│ 网关服务          │ 8080  │ http://localhost:8080           │"
echo "│ 用户服务          │ 8081  │ http://localhost:8081           │"
echo "│ 课程服务          │ 8082  │ http://localhost:8082           │"
echo "│ 认证服务          │ 8083  │ http://localhost:8083           │"
echo "│ Nacos控制台       │ 8848  │ http://localhost:8848/nacos     │"
echo "│ Sentinel控制台    │ 8858  │ http://localhost:8858           │"
echo "│ Druid监控(用户)   │ 8081  │ http://localhost:8081/druid     │"
echo "│ Druid监控(课程)   │ 8082  │ http://localhost:8082/druid     │"
echo "└─────────────────────────────────────────────────────────────┘"
echo
echo "📝 默认账号信息:"
echo "  Nacos: nacos/nacos"
echo "  Druid: admin/admin"
echo "  测试用户: admin/123456"
echo
echo "📁 日志文件位置: ./logs/"
echo
echo "🛑 停止所有服务: ./stop.sh"
echo

# 保存PID到文件
mkdir -p logs
echo $GATEWAY_PID > logs/gateway.pid
echo $USER_SERVICE_PID > logs/user-service.pid
echo $COURSE_SERVICE_PID > logs/course-service.pid
echo $AUTH_SERVICE_PID > logs/auth-service.pid

echo "✅ 启动完成! 请等待1-2分钟让所有服务完全启动。"