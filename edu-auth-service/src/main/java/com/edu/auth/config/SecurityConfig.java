package com.edu.auth.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;

/**
 * Spring Security 配置类
 */
@Configuration
@EnableWebSecurity
public class SecurityConfig {

    /**
     * 密码编码器
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    /**
     * 安全过滤器链配置
     */
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
            // 禁用CSRF保护，因为我们使用JWT
            .csrf().disable()
            // 禁用CORS
            .cors().disable()
            // 配置会话管理为无状态
            .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS)
            .and()
            // 配置授权规则
            .authorizeHttpRequests()
                // 允许注册和登录端点无需认证
                .antMatchers("/api/auth/register", "/api/auth/login", "/api/auth/validate").permitAll()
                // 允许健康检查端点
                .antMatchers("/actuator/**").permitAll()
                // 其他所有请求都需要认证
                .anyRequest().authenticated()
            .and()
            // 禁用默认的登录页面
            .formLogin().disable()
            // 禁用HTTP Basic认证
            .httpBasic().disable();

        return http.build();
    }
}
