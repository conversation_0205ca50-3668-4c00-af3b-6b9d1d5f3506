package com.edu.auth.feign;

import com.edu.common.result.Result;
import com.edu.user.entity.User;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 用户服务Feign客户端
 */
@FeignClient(name = "edu-user-service", path = "/api/user")
public interface UserServiceClient {

    /**
     * 根据用户名获取用户信息
     * @param username 用户名
     * @return 用户信息
     */
    @GetMapping("/username")
    Result<User> getUserByUsername(@RequestParam("username") String username);

    /**
     * 用户注册
     * @param user 用户信息
     * @return 注册结果
     */
    @PostMapping("/register")
    Result<Boolean> register(@RequestBody User user);

    /**
     * 用户登录验证
     * @param user 用户信息（包含用户名和密码）
     * @return 登录结果
     */
    @PostMapping("/login")
    Result<User> login(@RequestBody User user);
}