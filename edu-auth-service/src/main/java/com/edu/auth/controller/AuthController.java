package com.edu.auth.controller;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.edu.auth.dto.LoginRequest;
import com.edu.auth.dto.LoginResponse;
import com.edu.auth.dto.RegisterRequest;
import com.edu.auth.service.AuthService;
import com.edu.auth.util.JwtUtil;
import com.edu.common.result.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletRequest;

/**
 * 认证控制器
 */
@RestController
@RequestMapping("/api/auth")
public class AuthController {

    @Autowired
    private AuthService authService;

    @Autowired
    private JwtUtil jwtUtil;

    /**
     * 用户登录
     */
    @PostMapping("/login")
    @SentinelResource(value = "auth-login")
    public Result<LoginResponse> login(@RequestBody LoginRequest loginRequest) {
        try {
            LoginResponse response = authService.login(loginRequest);
            return Result.success("登录成功", response);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 用户注册
     */
    @PostMapping("/register")
    @SentinelResource(value = "auth-register")
    public Result<Boolean> register(@RequestBody RegisterRequest registerRequest) {
        try {
            boolean result = authService.register(registerRequest);
            return Result.success("注册成功", result);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 验证令牌
     */
    @PostMapping("/validate")
    @SentinelResource(value = "auth-validate")
    public Result<Boolean> validateToken(@RequestParam String token) {
        try {
            String username = jwtUtil.getUsernameFromToken(token);
            boolean isValid = jwtUtil.validateToken(token, username);
            return Result.success(isValid);
        } catch (Exception e) {
            return Result.error("令牌无效");
        }
    }

    /**
     * 刷新令牌
     */
    @PostMapping("/refresh")
    @SentinelResource(value = "auth-refresh")
    public Result<String> refreshToken(HttpServletRequest request) {
        try {
            String token = getTokenFromRequest(request);
            if (token == null) {
                return Result.error("令牌不能为空");
            }
            
            String newToken = jwtUtil.refreshToken(token);
            return Result.success("令牌刷新成功", newToken);
        } catch (Exception e) {
            return Result.error("令牌刷新失败");
        }
    }

    /**
     * 获取当前用户信息
     */
    @GetMapping("/userinfo")
    @SentinelResource(value = "auth-userinfo")
    public Result<Object> getUserInfo(HttpServletRequest request) {
        try {
            String token = getTokenFromRequest(request);
            if (token == null) {
                return Result.error("令牌不能为空");
            }
            
            String username = jwtUtil.getUsernameFromToken(token);
            Long userId = jwtUtil.getUserIdFromToken(token);
            String role = jwtUtil.getRoleFromToken(token);
            
            if (jwtUtil.isTokenExpired(token)) {
                return Result.error("令牌已过期");
            }
            
            return Result.success(new Object() {
                public final Long userId = getUserId();
                public final String username = getUsername();
                public final String role = getRole();
                
                public Long getUserId() { return userId; }
                public String getUsername() { return username; }
                public String getRole() { return role; }
            });
        } catch (Exception e) {
            return Result.error("获取用户信息失败");
        }
    }

    /**
     * 用户登出
     */
    @PostMapping("/logout")
    @SentinelResource(value = "auth-logout")
    public Result<Boolean> logout(HttpServletRequest request) {
        try {
            String token = getTokenFromRequest(request);
            if (token != null) {
                // 可以将令牌加入黑名单（Redis实现）
                authService.logout(token);
            }
            return Result.success("登出成功", true);
        } catch (Exception e) {
            return Result.error("登出失败");
        }
    }

    /**
     * 从请求中获取令牌
     */
    private String getTokenFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (bearerToken != null && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }
}