package com.edu.auth.service;

import com.edu.auth.dto.LoginRequest;
import com.edu.auth.dto.LoginResponse;
import com.edu.auth.dto.RegisterRequest;

/**
 * 认证服务接口
 */
public interface AuthService {

    /**
     * 用户登录
     * @param loginRequest 登录请求
     * @return 登录响应
     */
    LoginResponse login(LoginRequest loginRequest);

    /**
     * 用户注册
     * @param registerRequest 注册请求
     * @return 注册结果
     */
    boolean register(RegisterRequest registerRequest);

    /**
     * 用户登出
     * @param token JWT令牌
     */
    void logout(String token);

    /**
     * 验证令牌是否有效
     * @param token JWT令牌
     * @return 验证结果
     */
    boolean validateToken(String token);
}