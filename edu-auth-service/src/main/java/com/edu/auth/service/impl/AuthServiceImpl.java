package com.edu.auth.service.impl;

import com.edu.auth.dto.LoginRequest;
import com.edu.auth.dto.LoginResponse;
import com.edu.auth.dto.RegisterRequest;
import com.edu.auth.feign.UserServiceClient;
import com.edu.auth.service.AuthService;
import com.edu.auth.util.JwtUtil;
import com.edu.common.result.Result;
import com.edu.user.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import java.util.concurrent.TimeUnit;

/**
 * 认证服务实现类
 */
@Service
public class AuthServiceImpl implements AuthService {

    @Autowired
    private UserServiceClient userServiceClient;

    @Autowired
    private JwtUtil jwtUtil;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Value("${jwt.expiration:86400}")
    private Long jwtExpiration;

    private static final String TOKEN_BLACKLIST_PREFIX = "auth:blacklist:";

    @Override
    public LoginResponse login(LoginRequest loginRequest) {
        // 创建用户对象用于登录验证
        User loginUser = new User();
        loginUser.setUsername(loginRequest.getUsername());
        loginUser.setPassword(loginRequest.getPassword());

        // 调用用户服务进行登录验证
        Result<User> loginResult = userServiceClient.login(loginUser);
        if (!loginResult.isSuccess() || loginResult.getData() == null) {
            throw new RuntimeException(loginResult.getMessage() != null ? loginResult.getMessage() : "登录失败");
        }

        User user = loginResult.getData();
        
        // 生成JWT令牌
        String token = jwtUtil.generateToken(user.getUsername(), user.getId(), user.getRole());
        
        // 构建登录响应
        return new LoginResponse(token, jwtExpiration, user.getId(), user.getUsername(), user.getRole());
    }

    @Override
    public boolean register(RegisterRequest registerRequest) {
        // 验证密码确认
        if (!registerRequest.getPassword().equals(registerRequest.getConfirmPassword())) {
            throw new RuntimeException("两次输入的密码不一致");
        }

        // 创建用户对象
        User user = new User();
        user.setUsername(registerRequest.getUsername());
        user.setPassword(registerRequest.getPassword());
        user.setEmail(registerRequest.getEmail());
        user.setRealName(registerRequest.getRealName());
        user.setPhone(registerRequest.getPhone());
        user.setRole("STUDENT"); // 默认角色为学生
        user.setStatus(1); // 默认状态为启用

        // 调用用户服务进行注册
        Result<Boolean> registerResult = userServiceClient.register(user);
        if (!registerResult.isSuccess()) {
            throw new RuntimeException(registerResult.getMessage() != null ? registerResult.getMessage() : "注册失败");
        }

        return registerResult.getData();
    }

    @Override
    public void logout(String token) {
        // 将令牌加入黑名单
        String blacklistKey = TOKEN_BLACKLIST_PREFIX + token;
        redisTemplate.opsForValue().set(blacklistKey, "blacklisted", jwtExpiration, TimeUnit.SECONDS);
    }

    @Override
    public boolean validateToken(String token) {
        try {
            // 检查令牌是否在黑名单中
            String blacklistKey = TOKEN_BLACKLIST_PREFIX + token;
            if (redisTemplate.hasKey(blacklistKey)) {
                return false;
            }

            // 验证令牌格式和有效性
            String username = jwtUtil.getUsernameFromToken(token);
            return jwtUtil.validateToken(token, username);
        } catch (Exception e) {
            return false;
        }
    }
}