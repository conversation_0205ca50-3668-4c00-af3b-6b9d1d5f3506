#!/bin/bash

# 教育报名云平台停止脚本

echo "=== 教育报名云平台停止脚本 ==="
echo

# 停止微服务
echo "🛑 停止微服务..."

# 读取PID并停止服务
if [ -f "logs/gateway.pid" ]; then
    GATEWAY_PID=$(cat logs/gateway.pid)
    if ps -p $GATEWAY_PID > /dev/null 2>&1; then
        echo "停止网关服务 (PID: $GATEWAY_PID)..."
        kill $GATEWAY_PID
    fi
    rm -f logs/gateway.pid
fi

if [ -f "logs/user-service.pid" ]; then
    USER_SERVICE_PID=$(cat logs/user-service.pid)
    if ps -p $USER_SERVICE_PID > /dev/null 2>&1; then
        echo "停止用户服务 (PID: $USER_SERVICE_PID)..."
        kill $USER_SERVICE_PID
    fi
    rm -f logs/user-service.pid
fi

if [ -f "logs/course-service.pid" ]; then
    COURSE_SERVICE_PID=$(cat logs/course-service.pid)
    if ps -p $COURSE_SERVICE_PID > /dev/null 2>&1; then
        echo "停止课程服务 (PID: $COURSE_SERVICE_PID)..."
        kill $COURSE_SERVICE_PID
    fi
    rm -f logs/course-service.pid
fi

if [ -f "logs/auth-service.pid" ]; then
    AUTH_SERVICE_PID=$(cat logs/auth-service.pid)
    if ps -p $AUTH_SERVICE_PID > /dev/null 2>&1; then
        echo "停止认证服务 (PID: $AUTH_SERVICE_PID)..."
        kill $AUTH_SERVICE_PID
    fi
    rm -f logs/auth-service.pid
fi

# 强制停止可能残留的Java进程
echo "🔍 检查并停止残留的Spring Boot进程..."
pkill -f "spring-boot:run" 2>/dev/null || true
pkill -f "edu-gateway" 2>/dev/null || true
pkill -f "edu-user-service" 2>/dev/null || true
pkill -f "edu-course-service" 2>/dev/null || true
pkill -f "edu-auth-service" 2>/dev/null || true

sleep 3

# 停止Docker容器
# echo "🐳 停止Docker容器..."
# docker-compose down

echo
echo "✅ 所有服务已停止"
echo
echo "💡 提示:"
echo "  - 如需完全清理数据，请运行: docker-compose down -v"
echo "  - 如需重新启动，请运行: ./start.sh"
echo