package com.edu.course.controller;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.edu.common.result.Result;
import com.edu.course.entity.Course;
import com.edu.course.service.CourseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 课程控制器
 */
@RestController
@RequestMapping("/api/course")
public class CourseController {

    @Autowired
    private CourseService courseService;

    /**
     * 创建课程
     */
    @PostMapping
    @SentinelResource(value = "course-create")
    public Result<Boolean> createCourse(@RequestBody Course course) {
        course.setCurrentStudents(0); // 初始学生数为0
        course.setStatus(1); // 默认启用状态
        boolean result = courseService.save(course);
        return Result.success("课程创建成功", result);
    }

    /**
     * 根据ID查询课程
     */
    @GetMapping("/{id}")
    @SentinelResource(value = "course-get")
    public Result<Course> getCourseById(@PathVariable Long id) {
        Course course = courseService.getById(id);
        return Result.success(course);
    }

    /**
     * 分页查询课程
     */
    @GetMapping("/page")
    @SentinelResource(value = "course-page")
    public Result<IPage<Course>> getCoursePage(@RequestParam(defaultValue = "1") Integer current,
                                              @RequestParam(defaultValue = "10") Integer size,
                                              @RequestParam(required = false) String category) {
        Page<Course> page = new Page<>(current, size);
        IPage<Course> coursePage = courseService.page(page);
        return Result.success(coursePage);
    }

    /**
     * 更新课程信息
     */
    @PutMapping
    @SentinelResource(value = "course-update")
    public Result<Boolean> updateCourse(@RequestBody Course course) {
        boolean result = courseService.updateById(course);
        return Result.success("课程更新成功", result);
    }

    /**
     * 删除课程
     */
    @DeleteMapping("/{id}")
    @SentinelResource(value = "course-delete")
    public Result<Boolean> deleteCourse(@PathVariable Long id) {
        boolean result = courseService.removeById(id);
        return Result.success("课程删除成功", result);
    }

    /**
     * 课程报名
     */
    @PostMapping("/{id}/enroll")
    @SentinelResource(value = "course-enroll")
    public Result<Boolean> enrollCourse(@PathVariable Long id, @RequestParam Long userId) {
        try {
            boolean result = courseService.enrollCourse(id, userId);
            return Result.success("报名成功", result);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 取消报名
     */
    @PostMapping("/{id}/cancel")
    @SentinelResource(value = "course-cancel")
    public Result<Boolean> cancelEnrollment(@PathVariable Long id, @RequestParam Long userId) {
        try {
            boolean result = courseService.cancelEnrollment(id, userId);
            return Result.success("取消报名成功", result);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
}