package com.edu.course.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.edu.course.entity.Course;
import com.edu.course.mapper.CourseMapper;
import com.edu.course.service.CourseService;
import io.seata.spring.annotation.GlobalTransactional;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 课程服务实现类
 */
@Service
public class CourseServiceImpl extends ServiceImpl<CourseMapper, Course> implements CourseService {

    @Override
    public Course getCourseByCourseCode(String courseCode) {
        LambdaQueryWrapper<Course> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Course::getCourseCode, courseCode);
        return this.getOne(queryWrapper);
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    @Transactional(rollbackFor = Exception.class)
    public boolean enrollCourse(Long courseId, Long userId) {
        // 查询课程信息
        Course course = this.getById(courseId);
        if (course == null) {
            throw new RuntimeException("课程不存在");
        }
        
        if (course.getStatus() != 1) {
            throw new RuntimeException("课程已下架");
        }
        
        if (course.getCurrentStudents() >= course.getMaxStudents()) {
            throw new RuntimeException("课程已满员");
        }
        
        // 更新课程当前学生数
        course.setCurrentStudents(course.getCurrentStudents() + 1);
        boolean updateResult = this.updateById(course);
        
        if (!updateResult) {
            throw new RuntimeException("课程报名失败");
        }
        
        // 这里可以调用其他服务，比如创建报名记录、发送通知等
        // 由于是分布式事务，如果任何一个步骤失败，整个事务都会回滚
        
        return true;
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelEnrollment(Long courseId, Long userId) {
        // 查询课程信息
        Course course = this.getById(courseId);
        if (course == null) {
            throw new RuntimeException("课程不存在");
        }
        
        if (course.getCurrentStudents() <= 0) {
            throw new RuntimeException("当前课程无学生可取消");
        }
        
        // 更新课程当前学生数
        course.setCurrentStudents(course.getCurrentStudents() - 1);
        boolean updateResult = this.updateById(course);
        
        if (!updateResult) {
            throw new RuntimeException("取消报名失败");
        }
        
        return true;
    }
}