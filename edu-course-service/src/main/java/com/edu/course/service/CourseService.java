package com.edu.course.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.edu.course.entity.Course;

/**
 * 课程服务接口
 */
public interface CourseService extends IService<Course> {
    
    /**
     * 根据课程编码查询课程
     * @param courseCode 课程编码
     * @return 课程信息
     */
    Course getCourseByCourseCode(String courseCode);
    
    /**
     * 课程报名
     * @param courseId 课程ID
     * @param userId 用户ID
     * @return 报名结果
     */
    boolean enrollCourse(Long courseId, Long userId);
    
    /**
     * 取消报名
     * @param courseId 课程ID
     * @param userId 用户ID
     * @return 取消结果
     */
    boolean cancelEnrollment(Long courseId, Long userId);
}