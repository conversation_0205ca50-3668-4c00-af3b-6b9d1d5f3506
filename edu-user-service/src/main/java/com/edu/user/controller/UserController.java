package com.edu.user.controller;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.edu.common.result.Result;
import com.edu.user.entity.User;
import com.edu.user.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 用户控制器
 */
@RestController
@RequestMapping("/api/user")
public class UserController {

    @Autowired
    private UserService userService;

    /**
     * 用户注册
     */
    @PostMapping("/register")
    @SentinelResource(value = "user-register")
    public Result<Boolean> register(@RequestBody User user) {
        try {
            boolean result = userService.register(user);
            return Result.success("注册成功", result);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 用户登录
     */
    @PostMapping("/login")
    @SentinelResource(value = "user-login")
    public Result<User> login(@RequestParam String username, @RequestParam String password) {
        try {
            User user = userService.login(username, password);
            return Result.success("登录成功", user);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 根据ID查询用户
     */
    @GetMapping("/{id}")
    @SentinelResource(value = "user-get")
    public Result<User> getUserById(@PathVariable Long id) {
        User user = userService.getById(id);
        if (user != null) {
            user.setPassword(null); // 不返回密码
        }
        return Result.success(user);
    }

    /**
     * 分页查询用户
     */
    @GetMapping("/page")
    @SentinelResource(value = "user-page")
    public Result<IPage<User>> getUserPage(@RequestParam(defaultValue = "1") Integer current,
                                          @RequestParam(defaultValue = "10") Integer size) {
        Page<User> page = new Page<>(current, size);
        IPage<User> userPage = userService.page(page);
        // 清除密码信息
        userPage.getRecords().forEach(user -> user.setPassword(null));
        return Result.success(userPage);
    }

    /**
     * 更新用户信息
     */
    @PutMapping
    @SentinelResource(value = "user-update")
    public Result<Boolean> updateUser(@RequestBody User user) {
        // 不允许通过此接口修改密码
        user.setPassword(null);
        boolean result = userService.updateById(user);
        return Result.success("更新成功", result);
    }

    /**
     * 删除用户
     */
    @DeleteMapping("/{id}")
    @SentinelResource(value = "user-delete")
    public Result<Boolean> deleteUser(@PathVariable Long id) {
        boolean result = userService.removeById(id);
        return Result.success("删除成功", result);
    }
}