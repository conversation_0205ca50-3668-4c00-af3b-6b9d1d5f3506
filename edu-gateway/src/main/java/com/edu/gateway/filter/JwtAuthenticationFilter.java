package com.edu.gateway.filter;

import com.alibaba.fastjson.JSON;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * JWT认证过滤器
 */
@Component
public class JwtAuthenticationFilter implements GlobalFilter, Ordered {

    private final WebClient webClient;

    // 不需要认证的路径
    private static final List<String> EXCLUDE_PATHS = Arrays.asList(
            "/auth/login",
            "/auth/register",
            "/auth/validate",
            "/user/register",
            "/user/login"
    );

    public JwtAuthenticationFilter() {
        this.webClient = WebClient.builder().build();
    }

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        String path = request.getURI().getPath();

        // 检查是否为排除路径
        if (isExcludePath(path)) {
            return chain.filter(exchange);
        }

        // 获取Authorization头
        String authHeader = request.getHeaders().getFirst(HttpHeaders.AUTHORIZATION);
        if (!StringUtils.hasText(authHeader) || !authHeader.startsWith("Bearer ")) {
            return handleUnauthorized(exchange, "缺少认证令牌");
        }

        String token = authHeader.substring(7);

        // 调用认证服务验证令牌
        return validateToken(token)
                .flatMap(isValid -> {
                    if (isValid) {
                        // 令牌有效，继续处理请求
                        return chain.filter(exchange);
                    } else {
                        // 令牌无效
                        return handleUnauthorized(exchange, "令牌无效或已过期");
                    }
                })
                .onErrorResume(throwable -> {
                    // 验证过程中出现异常
                    return handleUnauthorized(exchange, "认证服务异常");
                });
    }

    /**
     * 检查是否为排除路径
     */
    private boolean isExcludePath(String path) {
        return EXCLUDE_PATHS.stream().anyMatch(path::startsWith);
    }

    /**
     * 调用认证服务验证令牌
     */
    private Mono<Boolean> validateToken(String token) {
        return webClient.post()
                .uri("http://edu-auth-service/api/auth/validate?token=" + token)
                .retrieve()
                .bodyToMono(String.class)
                .map(response -> {
                    try {
                        Map<String, Object> result = JSON.parseObject(response, Map.class);
                        return "200".equals(result.get("code").toString()) && 
                               Boolean.TRUE.equals(result.get("data"));
                    } catch (Exception e) {
                        return false;
                    }
                })
                .onErrorReturn(false);
    }

    /**
     * 处理未授权请求
     */
    private Mono<Void> handleUnauthorized(ServerWebExchange exchange, String message) {
        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(HttpStatus.UNAUTHORIZED);
        response.getHeaders().add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);

        Map<String, Object> result = new HashMap<>();
        result.put("code", "401");
        result.put("message", message);
        result.put("data", null);
        result.put("success", false);

        String body = JSON.toJSONString(result);
        DataBuffer buffer = response.bufferFactory().wrap(body.getBytes(StandardCharsets.UTF_8));
        return response.writeWith(Mono.just(buffer));
    }

    @Override
    public int getOrder() {
        return -100; // 优先级较高，在其他过滤器之前执行
    }
}