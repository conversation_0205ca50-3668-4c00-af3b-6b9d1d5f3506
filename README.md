# 教育报名云平台 (edu-enrollment-cloud)

基于Spring Cloud Alibaba的微服务教育报名平台，采用Nacos、Sentinel、Seata、MyBatis等技术栈。

## 技术栈

- **Spring Boot 2.7.14** - 基础框架
- **Spring Cloud 2021.0.8** - 微服务框架
- **Spring Cloud Alibaba 2021.0.5.0** - 阿里巴巴微服务组件
- **Nacos** - 服务注册发现与配置中心
- **Sentinel** - 流量控制与熔断降级
- **Seata** - 分布式事务
- **MyBatis Plus 3.5.3.1** - ORM框架
- **MySQL 8.0** - 数据库
- **Druid** - 数据库连接池

## 项目结构

```
edu-enrollment-cloud/
├── edu-common/              # 公共模块
│   ├── src/main/java/
│   │   └── com/edu/common/
│   │       ├── config/      # 配置类
│   │       ├── entity/      # 基础实体
│   │       └── result/      # 响应结果类
│   └── pom.xml
├── edu-gateway/             # 网关服务
│   ├── src/main/java/
│   │   └── com/edu/gateway/
│   ├── src/main/resources/
│   │   └── bootstrap.yml
│   └── pom.xml
├── edu-user-service/        # 用户服务
│   ├── src/main/java/
│   │   └── com/edu/user/
│   │       ├── controller/  # 控制器
│   │       ├── entity/      # 实体类
│   │       ├── mapper/      # Mapper接口
│   │       └── service/     # 服务层
│   ├── src/main/resources/
│   │   └── bootstrap.yml
│   └── pom.xml
├── edu-course-service/      # 课程服务
│   ├── src/main/java/
│   │   └── com/edu/course/
│   │       ├── controller/  # 控制器
│   │       └── entity/      # 实体类
│   ├── src/main/resources/
│   │   └── bootstrap.yml
│   └── pom.xml
├── edu-auth-service/        # 认证服务
│   ├── src/main/java/
│   │   └── com/edu/auth/
│   │       ├── controller/  # 控制器
│   │       ├── dto/         # 数据传输对象
│   │       ├── service/     # 服务层
│   │       ├── feign/       # Feign客户端
│   │       └── util/        # 工具类
│   ├── src/main/resources/
│   │   └── bootstrap.yml
│   └── pom.xml
├── sql/
│   └── init.sql            # 数据库初始化脚本
├── pom.xml                 # 父项目POM
└── README.md
```

## 环境要求

- JDK 8+
- Maven 3.6+
- MySQL 8.0+
- Nacos 2.2.0+
- Sentinel Dashboard 1.8.0+
- Seata Server 1.6.0+

## 快速启动

### 1. 环境准备

#### 启动Nacos
```bash
# 下载Nacos 2.2.0
wget https://github.com/alibaba/nacos/releases/download/2.2.0/nacos-server-2.2.0.tar.gz
tar -xzf nacos-server-2.2.0.tar.gz
cd nacos/bin

# 单机模式启动
sh startup.sh -m standalone
```

#### 启动Sentinel Dashboard
```bash
# 下载Sentinel Dashboard
wget https://github.com/alibaba/Sentinel/releases/download/1.8.0/sentinel-dashboard-1.8.0.jar

# 启动Dashboard
java -jar sentinel-dashboard-1.8.0.jar --server.port=8080
```

#### 启动Seata Server
```bash
# 下载Seata Server
wget https://github.com/seata/seata/releases/download/v1.6.0/seata-server-1.6.0.zip
unzip seata-server-1.6.0.zip
cd seata/bin

# 启动Seata Server
sh seata-server.sh -p 8091
```

### 2. 数据库初始化

```bash
# 连接MySQL并执行初始化脚本
mysql -u root -p < sql/init.sql
```

### 3. 启动服务

```bash
# 编译项目
mvn clean install

# 启动网关服务
cd edu-gateway
mvn spring-boot:run

# 启动用户服务
cd edu-user-service
mvn spring-boot:run

# 启动课程服务
cd edu-course-service
mvn spring-boot:run

# 启动认证服务
cd edu-auth-service
mvn spring-boot:run
```

## 服务端口

| 服务 | 端口   | 描述 |
|------|------|------|
| edu-gateway | 8080 | 网关服务 |
| edu-user-service | 8081 | 用户服务 |
| edu-course-service | 8082 | 课程服务 |
| edu-auth-service | 8083 | 认证服务 |
| Nacos | 8848 | 注册中心/配置中心 |
| Sentinel Dashboard | 8858 | 流控面板 |
| Seata Server | 8091 | 分布式事务协调器 |

## API接口

### 用户服务 API

- `POST /user/api/user/register` - 用户注册
- `POST /user/api/user/login` - 用户登录
- `GET /user/api/user/{id}` - 获取用户信息
- `GET /user/api/user/page` - 分页查询用户
- `PUT /user/api/user` - 更新用户信息
- `DELETE /user/api/user/{id}` - 删除用户

### 课程服务 API

- `POST /course/api/course` - 创建课程
- `GET /course/api/course/{id}` - 获取课程信息
- `GET /course/api/course/page` - 分页查询课程
- `PUT /course/api/course` - 更新课程信息
- `DELETE /course/api/course/{id}` - 删除课程
- `POST /course/api/course/{id}/enroll` - 课程报名

### 认证服务 API

- `POST /auth/api/auth/login` - 用户登录
- `POST /auth/api/auth/register` - 用户注册
- `POST /auth/api/auth/validate` - 验证令牌
- `POST /auth/api/auth/refresh` - 刷新令牌
- `GET /auth/api/auth/userinfo` - 获取当前用户信息
- `POST /auth/api/auth/logout` - 用户登出

## 配置说明

### Nacos配置
- 服务地址：localhost:8848
- 命名空间：public
- 用户名/密码：nacos/nacos

### 数据库配置
- 用户服务数据库：edu_user
- 课程服务数据库：edu_course
- 默认用户名/密码：root/123456

### 监控面板
- Nacos控制台：http://localhost:8848/nacos
- Sentinel控制台：http://localhost:8858
- Seata控制台：http://localhost:7091/
- Druid监控：http://localhost:8081/druid（用户服务）

## 注意事项

1. 确保MySQL、Nacos、Sentinel、Seata等中间件正常启动
2. 修改配置文件中的数据库连接信息
3. 首次启动需要执行数据库初始化脚本
4. 默认密码使用BCrypt加密，测试密码为：123456

## 开发指南

1. 新增服务时需要在父POM中添加模块
2. 服务间调用使用OpenFeign
3. 分布式事务使用@GlobalTransactional注解
4. 限流降级使用@SentinelResource注解
5. 统一异常处理和响应格式